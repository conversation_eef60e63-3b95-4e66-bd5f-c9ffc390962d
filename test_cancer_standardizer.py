#!/usr/bin/env python3
"""
Test script for the enhanced cancer term standardizer.

This script demonstrates the enhanced functionality including:
- Support for both text-embedding-3-large and text-embedding-ada-002
- Comprehensive logging and validation
- Processing of test data from markdown/results_sample
"""

import sys
import os
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from cancer_term_standardizer_openai import CancerTermStandardizerOpenAI
import logging

def test_basic_functionality():
    """Test basic standardization functionality."""
    print("=" * 80)
    print("🧪 TESTING BASIC FUNCTIONALITY")
    print("=" * 80)
    
    # Test with text-embedding-3-large
    print("\n📋 Testing with text-embedding-3-large model...")
    try:
        standardizer_large = CancerTermStandardizerOpenAI(
            cache_dir="cache_test_large",
            num_output=5,
            embedding_model="text-embedding-3-large"
        )
        
        # Test some cancer terms
        test_terms = [
            "breast cancer",
            "multiple myeloma", 
            "lung adenocarcinoma",
            "colon cancer"
        ]
        
        for term in test_terms:
            print(f"\n🔍 Testing term: '{term}'")
            results = standardizer_large.standardize(term)
            print(f"✅ Results: {results[:3]}")  # Show top 3 results
            
    except Exception as e:
        print(f"❌ Error with text-embedding-3-large: {e}")
    
    # Test with text-embedding-ada-002
    print("\n📋 Testing with text-embedding-ada-002 model...")
    try:
        standardizer_ada = CancerTermStandardizerOpenAI(
            cache_dir="cache_test_ada",
            num_output=5,
            embedding_model="text-embedding-ada-002"
        )
        
        # Test one term
        term = "breast cancer"
        print(f"\n🔍 Testing term: '{term}'")
        results = standardizer_ada.standardize(term)
        print(f"✅ Results: {results[:3]}")  # Show top 3 results
        
    except Exception as e:
        print(f"❌ Error with text-embedding-ada-002: {e}")

def test_cancer_type_subtype():
    """Test cancer type and subtype standardization."""
    print("\n" + "=" * 80)
    print("🧪 TESTING CANCER TYPE/SUBTYPE FUNCTIONALITY")
    print("=" * 80)
    
    try:
        standardizer = CancerTermStandardizerOpenAI(
            cache_dir="cache_test",
            num_output=3,
            embedding_model="text-embedding-3-large"
        )
        
        # Test type only
        print("\n📋 Testing cancer type only...")
        type_results = standardizer.get_standard_cancer_type_subtype("breast cancer")
        print(f"✅ Type results: {type_results}")
        
        # Test type and subtype
        print("\n📋 Testing cancer type and subtype...")
        type_subtype_results = standardizer.get_standard_cancer_type_subtype(
            cancer_type="multiple myeloma",
            cancer_subtype="relapsed/refractory multiple myeloma"
        )
        print(f"✅ Type/Subtype results: {type_subtype_results}")
        
    except Exception as e:
        print(f"❌ Error in type/subtype testing: {e}")

def test_validation_functionality():
    """Test the validation functionality."""
    print("\n" + "=" * 80)
    print("🧪 TESTING VALIDATION FUNCTIONALITY")
    print("=" * 80)
    
    try:
        standardizer = CancerTermStandardizerOpenAI(
            cache_dir="cache_test",
            num_output=3,
            embedding_model="text-embedding-3-large"
        )
        
        # Test validation with a term that should be standardized
        print("\n📋 Testing validation with 'breast cancer'...")
        results = standardizer.standardize("breast cancer")
        validation_result = standardizer._validate_standardization("breast cancer", results)
        print(f"✅ Validation result: {validation_result}")
        
        # Test validation with a term that might not change (edge case)
        print("\n📋 Testing validation with exact match scenario...")
        # This should trigger validation warning if the term comes back unchanged
        results = standardizer.standardize("cancer")
        validation_result = standardizer._validate_standardization("cancer", results)
        print(f"✅ Validation result: {validation_result}")
        
    except Exception as e:
        print(f"❌ Error in validation testing: {e}")

def test_sample_data_processing():
    """Test processing of sample data."""
    print("\n" + "=" * 80)
    print("🧪 TESTING SAMPLE DATA PROCESSING")
    print("=" * 80)
    
    # Check if test data exists
    test_data_dir = Path("markdown/results_sample")
    if not test_data_dir.exists():
        print(f"❌ Test data directory not found: {test_data_dir}")
        print("Skipping sample data processing test")
        return
    
    try:
        standardizer = CancerTermStandardizerOpenAI(
            cache_dir="cache_test",
            num_output=3,
            embedding_model="text-embedding-3-large"
        )
        
        print(f"\n📋 Processing test data from: {test_data_dir}")
        results = standardizer.process_test_data(str(test_data_dir))
        
        print(f"✅ Processing completed!")
        print(f"   Files processed: {len(results.get('processed_files', []))}")
        print(f"   Total cancer terms: {results.get('total_cancer_terms', 0)}")
        print(f"   Validation successes: {results.get('validation_successes', 0)}")
        print(f"   Validation failures: {results.get('validation_failures', 0)}")
        
    except Exception as e:
        print(f"❌ Error in sample data processing: {e}")

def main():
    """Run all tests."""
    print("🚀 STARTING CANCER TERM STANDARDIZER TESTS")
    print("=" * 80)
    
    # Set logging level to INFO to see detailed output
    logging.getLogger().setLevel(logging.INFO)
    
    try:
        # Run tests
        test_basic_functionality()
        test_cancer_type_subtype()
        test_validation_functionality()
        test_sample_data_processing()
        
        print("\n" + "=" * 80)
        print("✅ ALL TESTS COMPLETED")
        print("=" * 80)
        print("\nCheck the logs above for detailed standardization traces.")
        print("Look for validation warnings that indicate potential issues.")
        
    except Exception as e:
        print(f"\n❌ CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
