#!/usr/bin/env python3
"""
Simple script to run the cancer term standardizer on test data.

This script provides an easy way to test the standardizer functionality
without needing to understand all the implementation details.
"""

import sys
import os
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def main():
    """Run the standardizer on test data."""
    print("🚀 Running Cancer Term Standardizer Test")
    print("=" * 60)
    
    # Import and run the standardizer
    try:
        from cancer_term_standardizer_openai import main as standardizer_main
        
        # Set up arguments for test data processing
        sys.argv = [
            "cancer_term_standardizer_openai.py",
            "--test-data",
            "--embedding-model", "text-embedding-3-large",
            "--cache-dir", "cache_test",
            "--num-output", "5"
        ]
        
        print("📋 Processing test data with text-embedding-3-large model...")
        print("📁 Looking for test data in: markdown/results_sample/")
        print("💾 Using cache directory: cache_test/")
        print("🔢 Returning top 5 standardization results per term")
        print()
        
        # Run the standardizer
        standardizer_main()
        
        print("\n✅ Test completed! Check the logs above for detailed results.")
        print("📄 Results saved to: standardization_test_results_text_embedding_3_large.json")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("Make sure all required packages are installed:")
        print("  pip install openai numpy faiss-cpu oaklib python-dotenv tqdm")
        
    except FileNotFoundError as e:
        print(f"❌ File not found: {e}")
        print("Make sure the test data directory exists: markdown/results_sample/")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
