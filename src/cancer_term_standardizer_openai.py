"""
Cancer term standardizer using OpenAI embeddings and MONDO ontology.

This module provides functionality to standardize cancer terms using OpenAI's
embedding models (text-embedding-3-large or text-embedding-ada-002) and MONDO
ontology for semantic similarity matching. Includes comprehensive logging and
validation to ensure standardization quality.
"""

import os
import json
import pickle
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple, Union, Any
from dotenv import load_dotenv
import numpy as np
import faiss
import re
from difflib import SequenceMatcher
from collections import Counter

from openai import OpenAI, AzureOpenAI
from oaklib import get_adapter
from oaklib.datamodels.vocabulary import IS_A
from tqdm import tqdm

# Load environment variables
load_dotenv()

# Configure comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('cancer_standardizer.log')
    ]
)
logger = logging.getLogger(__name__)

class CancerTermStandardizerOpenAI:
    """
    Cancer term standardizer using OpenAI embeddings and MONDO ontology.

    Supports both text-embedding-3-large and text-embedding-ada-002 models
    with comprehensive logging and validation capabilities.
    """

    def __init__(self,
                 cache_dir: str = "cache",
                 num_output: int = 10,
                 batch_size: int = 100,
                 embedding_model: str = "text-embedding-3-large"):
        """
        Initialize the cancer term standardizer.

        Args:
            cache_dir: Directory to store cached files
            num_output: Number of matching terms to return
            batch_size: Batch size for OpenAI API calls
            embedding_model: OpenAI embedding model to use
                           ('text-embedding-3-large' or 'text-embedding-ada-002')
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)

        self.num_output = num_output
        self.batch_size = batch_size

        # Validate and set embedding model
        supported_models = ["text-embedding-3-large", "text-embedding-ada-002"]
        if embedding_model not in supported_models:
            raise ValueError(f"Unsupported embedding model: {embedding_model}. "
                           f"Supported models: {supported_models}")
        self.embedding_model = embedding_model

        # Set embedding dimensions based on model
        self.embedding_dimensions = {
            "text-embedding-3-large": 3072,
            "text-embedding-ada-002": 1536
        }[embedding_model]

        # Initialize OpenAI client
        self.client = self._get_openai_client()

        logger.info(f"Initialized OpenAI cancer term standardizer with model: {embedding_model}")
        logger.info(f"Embedding dimensions: {self.embedding_dimensions}")

        # File paths for persistent storage (include model name for caching)
        model_suffix = embedding_model.replace("-", "_")
        self.cancer_terms_file = self.cache_dir / f'cancer_terms_{model_suffix}.pkl'
        self.cancer_embeddings_file = self.cache_dir / f'cancer_embeddings_{model_suffix}.pkl'
        self.cancer_synonyms_file = self.cache_dir / 'cancer_synonyms.json'

        # MONDO cancer ID
        self.cancer_mondo_id = 'MONDO:0004992'

        # Templates
        self.query_template = "{term} appears in our document in the following context: {context}"
        self.chunk_template = "{term} is defined as {definition}"

        # Initialize cancer terms if not already cached
        self._ensure_cancer_terms_loaded()

        # Initialize empty biological context structures for now
        self.term_to_ancestors = {}
        self.term_clusters = {}
        self.term_specificity = {}

        # Initialize MONDO ontology validation
        self._initialize_mondo_validation()

        # TODO: Re-enable dynamic biological context after debugging
        # self._initialize_dynamic_biological_context()

    def _get_openai_client(self):
        """Get OpenAI client - automatically detects Azure vs regular OpenAI based on credentials"""
        # Check if Azure credentials are provided (try multiple key name variations)
        azure_key = os.getenv("AZURE_OPENAI_KEY") or os.getenv("AZURE_OPENAI_API_KEY")
        azure_endpoint = os.getenv("AZURE_OPENAI_ENDPOINT")

        if azure_endpoint and azure_key:
            logger.info("Using Azure OpenAI client")
            return AzureOpenAI(
                azure_endpoint=azure_endpoint,
                api_key=azure_key,
                api_version=os.getenv("AZURE_OPENAI_API_VERSION", "2024-12-01-preview")
            )
        # Fall back to regular OpenAI
        elif os.getenv("OPENAI_API_KEY"):
            logger.info("Using regular OpenAI client")
            return OpenAI(
                api_key=os.getenv("OPENAI_API_KEY")
            )
        else:
            raise ValueError(
                "No OpenAI credentials found. Please provide either:\n"
                "- OPENAI_API_KEY for regular OpenAI, or\n"
                "- AZURE_OPENAI_ENDPOINT and AZURE_OPENAI_API_KEY (or AZURE_OPENAI_KEY) for Azure OpenAI"
            )

    def _get_embeddings(self, texts: List[str]) -> np.ndarray:
        """Get embeddings using the configured OpenAI embedding model."""
        logger.info(f"Getting embeddings for {len(texts)} texts using model: {self.embedding_model}")
        embeddings = []

        # Process in batches to respect API limits
        for i in range(0, len(texts), self.batch_size):
            batch = texts[i:i + self.batch_size]
            batch_num = i//self.batch_size + 1
            total_batches = (len(texts) + self.batch_size - 1)//self.batch_size

            logger.info(f"Processing batch {batch_num}/{total_batches} with {len(batch)} texts")

            try:
                response = self.client.embeddings.create(
                    model=self.embedding_model,
                    input=batch,
                    encoding_format="float"
                )

                batch_embeddings = [data.embedding for data in response.data]
                embeddings.extend(batch_embeddings)

                logger.info(f"Successfully processed batch {batch_num}/{total_batches}")

            except Exception as e:
                logger.error(f"Error getting embeddings for batch {batch_num}: {e}")
                # Return zero embeddings for failed batch using correct dimensions
                batch_embeddings = [[0.0] * self.embedding_dimensions] * len(batch)
                embeddings.extend(batch_embeddings)
                logger.warning(f"Using zero embeddings for failed batch {batch_num}")

        logger.info(f"Generated embeddings with shape: {np.array(embeddings).shape}")
        return np.array(embeddings)

    def _get_mondo_descendants(self, curie: str, adapter) -> List[Tuple[str, str]]:
        """Get all descendants of a MONDO term."""
        all_descendants_data = [
            (adapter.label(child), child)
            for child in adapter.descendants([curie], predicates=[IS_A])
            if child != curie and adapter.label(child) is not None
        ]
        return all_descendants_data

    def _save_synonyms(self, cancers: List[Tuple[str, str]], adapter):
        """Save cancer term synonyms to file."""
        synonyms_dict = {}
        for term, curie in cancers:
            synonyms = adapter.entity_metadata_map(curie).get('oio:hasExactSynonym')
            if synonyms is None:
                synonyms = []
            synonyms.append(term)
            for synonym in synonyms:
                if synonym not in synonyms_dict:
                    synonyms_dict[synonym] = term

        with open(self.cancer_synonyms_file, 'w') as f:
            json.dump(synonyms_dict, f)

    def _get_docs_and_terms(self, cancers: List[Tuple[str, str]], adapter) -> Tuple[List[str], List[str]]:
        """Generate documents and terms for embedding."""
        documents = []
        terms = []

        for term, curie in cancers:
            terms.append(term)
            definition = adapter.definition(curie)
            if definition:
                document = self.chunk_template.format(term=term, definition=definition)
            else:
                document = f"{term} is a type of cancer"
            documents.append(document)

        return documents, terms

    def _fetch_and_save_cancer_terms(self):
        """Fetch cancer terms from MONDO and save embeddings."""
        logger.info("Fetching cancer terms from MONDO ontology...")

        # Initialize MONDO adapter
        logger.info("Initializing MONDO adapter...")
        adapter = get_adapter("sqlite:obo:mondo")

        # Get cancer descendants
        logger.info(f"Getting descendants of MONDO cancer term: {self.cancer_mondo_id}")
        cancers = self._get_mondo_descendants(self.cancer_mondo_id, adapter)
        logger.info(f"Found {len(cancers)} cancer terms from MONDO")

        # Save synonyms
        logger.info("Saving cancer term synonyms...")
        self._save_synonyms(cancers, adapter)

        # Generate documents and terms
        logger.info("Generating documents and terms for embedding...")
        documents, terms = self._get_docs_and_terms(cancers, adapter)
        logger.info(f"Generated {len(documents)} documents and {len(terms)} terms")

        # Get embeddings
        logger.info(f"Generating embeddings using {self.embedding_model}...")
        embeddings = self._get_embeddings(documents)

        # Save terms and embeddings
        logger.info(f"Saving terms to: {self.cancer_terms_file}")
        with open(self.cancer_terms_file, 'wb') as f:
            pickle.dump(terms, f)

        logger.info(f"Saving embeddings to: {self.cancer_embeddings_file}")
        with open(self.cancer_embeddings_file, 'wb') as f:
            pickle.dump(embeddings, f)

        logger.info("Cancer terms and embeddings saved successfully")

        # Initialize empty biological context structures for now
        self.term_to_ancestors = {}
        self.term_clusters = {}
        self.term_specificity = {}
        logger.info("Initialized empty biological context structures")

    def _ensure_cancer_terms_loaded(self):
        """Ensure cancer terms and embeddings are loaded."""
        files_exist = all(
            f.exists() for f in [
                self.cancer_terms_file,
                self.cancer_embeddings_file,
                self.cancer_synonyms_file
            ]
        )

        if not files_exist:
            logger.info("Cancer terms not found, fetching from MONDO...")
            self._fetch_and_save_cancer_terms()
        else:
            # Terms already exist - initialize empty biological context for now
            if not hasattr(self, 'term_clusters'):
                self.term_to_ancestors = {}
                self.term_clusters = {}
                self.term_specificity = {}
                logger.info("Initialized empty biological context structures")

    def standardize(self, term: str, context: Optional[str] = None) -> List[str]:
        """
        Two-stage standardization: Embedding similarity + LLM biological context filtering.

        Args:
            term: The cancer term to standardize
            context: Optional context for the term

        Returns:
            List of standardized cancer terms ranked by biological appropriateness
        """
        logger.info(f"🔍 STARTING TWO-STAGE STANDARDIZATION for: '{term}'")
        if context:
            logger.info(f"📝 Context provided: '{context}'")

        # Load synonyms dict
        logger.info("Loading synonyms dictionary...")
        with open(self.cancer_synonyms_file, 'r') as f:
            synonyms_dict = json.load(f)

        # Check for exact synonym match first
        if term in synonyms_dict:
            exact_match = synonyms_dict[term]
            logger.info(f"✅ EXACT SYNONYM MATCH found: '{term}' -> '{exact_match}'")

            # Validate standardization quality
            if self._validate_standardization(term, [exact_match]):
                return [exact_match]
            else:
                logger.warning(f"⚠️ Exact match validation failed, proceeding with two-stage search")

        # STAGE 1: Expanded Similarity Search (200 candidates)
        logger.info("🎯 STAGE 1: Expanded embedding-based similarity search...")
        stage1_candidates = self._stage1_expanded_similarity_search(term, context)

        # STAGE 2: LLM-Based Biological Context Filtering
        logger.info("🧠 STAGE 2: LLM-based biological context filtering...")
        final_results = self._stage2_llm_biological_filtering(term, stage1_candidates, context)

        logger.info(f"✅ TWO-STAGE STANDARDIZATION COMPLETED for '{term}'")
        return final_results

    def _stage1_expanded_similarity_search(self, term: str, context: Optional[str] = None) -> List[str]:
        """Stage 1: Get top 200 candidates using embedding similarity."""
        # Load embeddings and terms
        logger.info("Loading cancer embeddings and terms...")
        with open(self.cancer_embeddings_file, 'rb') as f:
            embeddings = pickle.load(f)

        with open(self.cancer_terms_file, 'rb') as f:
            terms = pickle.load(f)

        logger.info(f"Loaded {len(terms)} cancer terms and {embeddings.shape} embeddings")

        # Create FAISS index
        logger.info("Creating FAISS index for similarity search...")
        index = faiss.IndexFlatL2(embeddings.shape[1])
        index.add(embeddings)

        # Generate query
        if context is None:
            query = term
            logger.info(f"Using term as query: '{query}'")
        else:
            query = self.query_template.format(term=term, context=context)
            logger.info(f"Generated contextual query: '{query}'")

        # Get query embedding
        logger.info("Getting query embedding...")
        query_embedding = self._get_embeddings([query])

        # Search for 200 candidates for LLM filtering
        search_candidates = min(200, len(terms))
        logger.info(f"Searching for {search_candidates} candidate terms for LLM filtering...")
        distances, indices = index.search(query_embedding, search_candidates)

        # Return candidate terms with MONDO validation
        candidates = []
        for idx, distance in zip(indices[0], distances[0]):
            candidates.append(terms[idx])

        # Apply MONDO validation to filter only valid ontology terms
        validated_candidates = self._filter_mondo_valid_terms(candidates)

        logger.info(f"Stage 1 completed: Retrieved {len(candidates)} candidates, {len(validated_candidates)} MONDO-validated")
        return validated_candidates

    def _stage2_llm_biological_filtering(self, original_term: str, candidates: List[str], context: Optional[str] = None) -> List[str]:
        """Stage 2: Use LLM to filter candidates based on biological appropriateness."""
        logger.info(f"Starting LLM biological filtering for {len(candidates)} candidates...")

        # Prepare the LLM prompt
        prompt = self._create_biological_filtering_prompt(original_term, candidates, context)

        try:
            # Call the LLM for biological context filtering
            response = self.client.chat.completions.create(
                model="gpt-4o",  # Use GPT-4o for better medical knowledge
                messages=[
                    {
                        "role": "system",
                        "content": "You are a medical oncology expert with deep knowledge of cancer terminology, biological hierarchies, and appropriate cancer classification. Your task is to filter and rank cancer terms based on biological appropriateness and hierarchical relationships."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,  # Low temperature for consistent medical reasoning
                max_tokens=2000
            )

            # Parse the LLM response
            llm_response = response.choices[0].message.content
            logger.info("LLM biological filtering completed")

            # Extract the filtered results
            filtered_results = self._parse_llm_filtering_response(llm_response)

            # Apply MONDO validation to LLM results
            validated_results = self._validate_and_prioritize_llm_results(filtered_results)

            # Log the LLM reasoning
            logger.info(f"🧠 LLM BIOLOGICAL FILTERING RESULTS for '{original_term}':")
            for i, result in enumerate(validated_results[:10], 1):
                logger.info(f"  {i}. {result['term']} (confidence: {result['confidence']:.2f}) - {result['explanation']}")

            # Return just the term names for compatibility
            return [result['term'] for result in validated_results[:self.num_output]]

        except Exception as e:
            logger.error(f"LLM biological filtering failed: {e}")
            logger.warning("Falling back to top candidates from Stage 1")
            return candidates[:self.num_output]

    def _create_biological_filtering_prompt(self, original_term: str, candidates: List[str], context: Optional[str] = None) -> str:
        """Create a detailed prompt for LLM biological filtering."""

        context_info = f"\nAdditional context: {context}" if context else ""

        prompt = f"""
You are tasked with filtering and ranking cancer terms based on biological appropriateness and hierarchical relationships.

ORIGINAL TERM TO STANDARDIZE: "{original_term}"{context_info}

CANDIDATE TERMS (from embedding similarity search):
{chr(10).join([f"{i+1}. {term}" for i, term in enumerate(candidates)])}

INSTRUCTIONS:
1. **MONDO ONTOLOGY COMPLIANCE**: Only select terms that exist exactly as defined in the MONDO ontology database
2. **Anatomical Specificity Priority**: When multiple valid options exist, prioritize anatomically specific terms (e.g., "Ewing sarcoma of bone" over generic "Ewing sarcoma")
3. **Biological Hierarchy Analysis**: Analyze relationships between the original term and each candidate
4. **Specificity Level Matching**:
   - If the original term is general (e.g., "hematological malignancy"), prioritize broader categories over highly specific subtypes
   - If the original term is specific, match to appropriately specific terms
5. **Biological Appropriateness**: Filter out biologically inappropriate matches (e.g., solid tumors when looking for hematological malignancies)
6. **Select 10 most appropriate terms** with confidence scores (0.0-1.0) and explanations

BIOLOGICAL CONTEXT CONSIDERATIONS:
- Hematological malignancies include: leukemias, lymphomas, myelomas, and other blood cancers
- Solid tumors include: carcinomas, sarcomas, and organ-specific cancers
- Consider anatomical locations, cell types, and disease progression patterns
- Prioritize terms that match the biological category and specificity level
- **CRITICAL**: All selected terms must be legitimate MONDO ontology entries
- **CRITICAL**: In cases where a broader term is asked to be standardized DO NOT give a more specific term as an option. For example, if "meningioma" is asked to be standardized, do not give "cerebral meningioma" as an option.

OUTPUT FORMAT (exactly as shown):
1. [term] (confidence: 0.XX) - [brief explanation]
2. [term] (confidence: 0.XX) - [brief explanation]
...
10. [term] (confidence: 0.XX) - [brief explanation]

Provide your analysis:"""

        return prompt

    def _parse_llm_filtering_response(self, llm_response: str) -> List[Dict[str, Any]]:
        """Parse the LLM response to extract filtered results."""
        results = []

        lines = llm_response.strip().split('\n')

        for line in lines:
            line = line.strip()
            if not line or not line[0].isdigit():
                continue

            try:
                # Parse format: "1. term (confidence: 0.XX) - explanation"
                # Extract the term
                if ') -' in line:
                    term_part = line.split(') -')[0]
                    explanation = line.split(') -')[1].strip()
                else:
                    term_part = line
                    explanation = "No explanation provided"

                # Extract confidence score
                confidence_match = re.search(r'confidence:\s*([\d.]+)', term_part)
                confidence = float(confidence_match.group(1)) if confidence_match else 0.5

                # Extract term name (remove number and confidence part)
                term_match = re.search(r'^\d+\.\s*(.+?)\s*\(confidence:', term_part)
                if term_match:
                    term = term_match.group(1).strip()
                else:
                    # Fallback parsing
                    term = re.sub(r'^\d+\.\s*', '', term_part)
                    term = re.sub(r'\s*\(confidence:.*', '', term)
                    term = term.strip()

                # Clean up markdown formatting and other artifacts
                term = re.sub(r'\*\*(.+?)\*\*', r'\1', term)  # Remove **bold** formatting
                term = re.sub(r'\*(.+?)\*', r'\1', term)      # Remove *italic* formatting
                term = re.sub(r'^[\*\-\+]\s*', '', term)      # Remove bullet points
                term = term.strip()

                if term:
                    results.append({
                        'term': term,
                        'confidence': confidence,
                        'explanation': explanation
                    })

            except Exception as e:
                logger.debug(f"Failed to parse LLM response line: '{line}' - {e}")
                continue

        # Sort by confidence score (descending)
        results.sort(key=lambda x: x['confidence'], reverse=True)

        logger.info(f"Successfully parsed {len(results)} results from LLM response")
        return results

    def _initialize_mondo_validation(self):
        """Initialize MONDO ontology validation by creating a set of valid cancer terms."""
        logger.info("Initializing MONDO ontology validation...")

        try:
            # Load the cancer terms that were extracted from MONDO
            with open(self.cancer_terms_file, 'rb') as f:
                cancer_terms = pickle.load(f)

            # Create a set for fast lookup of valid MONDO terms
            self.valid_mondo_terms = set(cancer_terms)

            # Create a lowercase mapping for case-insensitive validation
            self.valid_mondo_terms_lower = {term.lower(): term for term in cancer_terms}

            logger.info(f"Loaded {len(self.valid_mondo_terms)} valid MONDO cancer terms for validation")

        except Exception as e:
            logger.warning(f"Could not initialize MONDO validation: {e}")
            self.valid_mondo_terms = set()
            self.valid_mondo_terms_lower = {}

    def _validate_mondo_term(self, term: str) -> bool:
        """Validate if a term exists in the MONDO ontology."""
        if not hasattr(self, 'valid_mondo_terms'):
            return True  # Skip validation if not initialized

        # Check exact match first
        if term in self.valid_mondo_terms:
            return True

        # Check case-insensitive match
        if term.lower() in self.valid_mondo_terms_lower:
            return True

        return False

    def _get_canonical_mondo_term(self, term: str) -> str:
        """Get the canonical MONDO term for a given term (handles case differences)."""
        if not hasattr(self, 'valid_mondo_terms_lower'):
            return term

        # Return exact match if exists
        if term in self.valid_mondo_terms:
            return term

        # Return canonical case if case-insensitive match exists
        if term.lower() in self.valid_mondo_terms_lower:
            return self.valid_mondo_terms_lower[term.lower()]

        return term

    def _filter_mondo_valid_terms(self, terms: List[str]) -> List[str]:
        """Filter a list of terms to only include valid MONDO terms."""
        if not hasattr(self, 'valid_mondo_terms'):
            return terms  # Skip filtering if validation not initialized

        valid_terms = []
        invalid_count = 0

        for term in terms:
            if self._validate_mondo_term(term):
                # Get canonical form (correct case)
                canonical_term = self._get_canonical_mondo_term(term)
                valid_terms.append(canonical_term)
            else:
                invalid_count += 1
                logger.debug(f"Filtered out non-MONDO term: '{term}'")

        if invalid_count > 0:
            logger.info(f"Filtered out {invalid_count} non-MONDO terms, kept {len(valid_terms)} valid terms")

        return valid_terms

    def _validate_and_prioritize_llm_results(self, llm_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Validate LLM results against MONDO and prioritize anatomically specific terms."""
        validated_results = []

        for result in llm_results:
            term = result['term']

            # Validate against MONDO ontology
            if self._validate_mondo_term(term):
                # Get canonical MONDO term (correct case)
                canonical_term = self._get_canonical_mondo_term(term)

                # Update result with canonical term
                validated_result = result.copy()
                validated_result['term'] = canonical_term

                # Boost confidence for anatomically specific terms
                specificity_boost = self._calculate_anatomical_specificity_boost(canonical_term)
                validated_result['confidence'] = min(1.0, result['confidence'] + specificity_boost)

                # Add MONDO validation note to explanation
                if canonical_term != term:
                    validated_result['explanation'] += f" (MONDO canonical: {canonical_term})"

                validated_results.append(validated_result)
            else:
                logger.warning(f"LLM suggested non-MONDO term: '{term}' - filtering out")

        # Sort by confidence (higher is better) to prioritize specific terms
        validated_results.sort(key=lambda x: x['confidence'], reverse=True)

        logger.info(f"MONDO validation: {len(llm_results)} LLM results → {len(validated_results)} validated")
        return validated_results

    def _calculate_anatomical_specificity_boost(self, term: str) -> float:
        """Calculate confidence boost for anatomically specific terms."""
        term_lower = term.lower()

        # Anatomical location indicators that suggest specificity
        anatomical_indicators = [
            'of bone', 'of liver', 'of lung', 'of breast', 'of brain', 'of kidney',
            'of stomach', 'of colon', 'of rectum', 'of pancreas', 'of prostate',
            'of ovary', 'of uterus', 'of cervix', 'of bladder', 'of skin',
            'of thyroid', 'of esophagus', 'of head and neck', 'of spine',
            'bone', 'hepatic', 'pulmonary', 'mammary', 'cerebral', 'renal',
            'gastric', 'colonic', 'rectal', 'pancreatic', 'prostatic',
            'ovarian', 'uterine', 'cervical', 'vesical', 'cutaneous',
            'thyroidal', 'esophageal', 'spinal'
        ]

        # Histological specificity indicators
        histological_indicators = [
            'adenocarcinoma', 'squamous cell carcinoma', 'ductal carcinoma',
            'lobular carcinoma', 'clear cell', 'papillary', 'follicular',
            'medullary', 'mucinous', 'serous', 'endometrioid'
        ]

        # Genetic/molecular specificity indicators
        molecular_indicators = [
            'FLT3', 'IDH1', 'IDH2', 'NPM1', 'CEBPA', 'KIT', 'PDGFRA',
            'BCR-ABL', 'PML-RARA', 'RUNX1-RUNX1T1', 'CBFB-MYH11',
            'MLL', 'TP53', 'EGFR', 'KRAS', 'BRAF', 'PIK3CA', 'HER2'
        ]

        boost = 0.0

        # Boost for anatomical specificity
        for indicator in anatomical_indicators:
            if indicator in term_lower:
                boost += 0.05
                break  # Only count once per category

        # Boost for histological specificity
        for indicator in histological_indicators:
            if indicator in term_lower:
                boost += 0.03
                break

        # Boost for molecular specificity
        for indicator in molecular_indicators:
            if indicator in term_lower:
                boost += 0.02
                break

        # Cap the boost to prevent over-inflation
        return min(boost, 0.10)

    def standardize_with_explanations(self, term: str, context: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Two-stage standardization with detailed explanations and confidence scores.

        Returns:
            List of dictionaries with 'term', 'confidence', and 'explanation' keys
        """
        logger.info(f"🔍 STARTING DETAILED TWO-STAGE STANDARDIZATION for: '{term}'")

        # STAGE 1: Expanded Similarity Search (200 candidates)
        logger.info("🎯 STAGE 1: Expanded embedding-based similarity search...")
        stage1_candidates = self._stage1_expanded_similarity_search(term, context)

        # STAGE 2: LLM-Based Biological Context Filtering with explanations
        logger.info("🧠 STAGE 2: LLM-based biological context filtering...")

        # Prepare the LLM prompt
        prompt = self._create_biological_filtering_prompt(term, stage1_candidates, context)

        try:
            # Call the LLM for biological context filtering
            response = self.client.chat.completions.create(
                model="gpt-4o",  # Use GPT-4o for better medical knowledge
                messages=[
                    {
                        "role": "system",
                        "content": "You are a medical oncology expert with deep knowledge of cancer terminology, biological hierarchies, and appropriate cancer classification. Your task is to filter and rank cancer terms based on biological appropriateness and hierarchical relationships."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.1,  # Low temperature for consistent medical reasoning
                max_tokens=2000
            )

            # Parse the LLM response
            llm_response = response.choices[0].message.content
            logger.info("LLM biological filtering completed")

            # Extract the filtered results with explanations
            filtered_results = self._parse_llm_filtering_response(llm_response)

            # Apply MONDO validation to LLM results
            validated_results = self._validate_and_prioritize_llm_results(filtered_results)

            logger.info(f"✅ DETAILED TWO-STAGE STANDARDIZATION COMPLETED for '{term}'")
            return validated_results[:self.num_output]

        except Exception as e:
            logger.error(f"LLM biological filtering failed: {e}")
            logger.warning("Falling back to top candidates from Stage 1")
            # Return fallback results without explanations (with MONDO validation)
            fallback_results = []
            validated_candidates = self._filter_mondo_valid_terms(stage1_candidates[:self.num_output])

            for candidate in validated_candidates:
                canonical_term = self._get_canonical_mondo_term(candidate)
                fallback_results.append({
                    'term': canonical_term,
                    'confidence': 0.5,
                    'explanation': 'Fallback result from embedding similarity (LLM filtering failed, MONDO validated)'
                })
            return fallback_results

    def _validate_standardization(self, original_term: str, standardized_terms: List[str]) -> bool:
        """
        Validate if standardization is working correctly.

        If the same term appears in both input and standardized output without any changes,
        this indicates the standardizer is likely not functioning properly (99% confidence).

        Args:
            original_term: The original term to standardize
            standardized_terms: List of standardized terms

        Returns:
            True if standardization appears to be working, False otherwise
        """
        if not standardized_terms:
            logger.warning(f"❌ VALIDATION FAILED: No standardized terms returned for '{original_term}'")
            return False

        # Check if the exact same term appears as the top result
        top_result = standardized_terms[0]
        if original_term.lower().strip() == top_result.lower().strip():
            logger.warning(f"❌ VALIDATION FAILED: Original term '{original_term}' unchanged in standardization -> '{top_result}'")
            logger.warning("This indicates standardization is likely not working properly (99% confidence)")
            return False

        logger.info(f"✅ VALIDATION PASSED: '{original_term}' -> '{top_result}' (terms are different)")
        return True

    def _initialize_dynamic_biological_context(self):
        """Initialize dynamic biological context from MONDO ontology and loaded cancer terms."""
        logger.info("Initializing dynamic biological context from MONDO ontology...")

        # Initialize biological context structures
        self.term_to_ancestors = {}  # Maps each term to its MONDO ancestors
        self.term_clusters = {}      # Groups terms by semantic similarity
        self.term_specificity = {}   # Measures how specific vs generic each term is

        # Load the cancer terms that were already processed
        try:
            with open(self.cancer_terms_file, 'rb') as f:
                cancer_terms = pickle.load(f)

            logger.info(f"Building dynamic biological context for {len(cancer_terms)} cancer terms...")

            # Build term specificity scores (more tokens = more specific)
            self._build_term_specificity_scores(cancer_terms)

            # Build semantic clusters using term similarity
            self._build_semantic_clusters(cancer_terms)

            # Build MONDO hierarchical relationships if available (optional - can be slow)
            # Skip for now to improve performance - can be enabled later if needed
            # self._build_mondo_hierarchical_context(cancer_terms)
            logger.info("Skipping MONDO hierarchical context for performance (can be enabled if needed)")

            logger.info(f"Dynamic biological context initialized with {len(self.term_clusters)} semantic clusters")

        except FileNotFoundError:
            logger.warning("Cancer terms not yet loaded - biological context will be initialized after term loading")
            # Set empty structures that will be populated later
            self.term_to_ancestors = {}
            self.term_clusters = {}
            self.term_specificity = {}

    def _build_term_specificity_scores(self, cancer_terms: List[str]):
        """Build specificity scores for cancer terms based on their complexity."""
        logger.info("Building term specificity scores...")

        for term in cancer_terms:
            # Calculate specificity based on multiple factors
            tokens = term.lower().split()

            # More tokens generally means more specific
            token_score = len(tokens) / 10.0  # Normalize to 0-1 range

            # Presence of specific descriptors increases specificity
            specific_descriptors = ['acute', 'chronic', 'primary', 'secondary', 'metastatic',
                                  'invasive', 'in situ', 'stage', 'grade', 'positive', 'negative',
                                  'receptor', 'hereditary', 'familial', 'sporadic', 'therapy-related']

            descriptor_score = sum(1 for desc in specific_descriptors if desc in term.lower()) / 5.0

            # Presence of anatomical locations increases specificity
            anatomical_indicators = ['left', 'right', 'upper', 'lower', 'central', 'peripheral',
                                   'anterior', 'posterior', 'medial', 'lateral']
            anatomical_score = sum(1 for anat in anatomical_indicators if anat in term.lower()) / 3.0

            # Combine scores (cap at 1.0)
            specificity = min(1.0, token_score + descriptor_score + anatomical_score)
            self.term_specificity[term] = specificity

        logger.info(f"Built specificity scores for {len(self.term_specificity)} terms")

    def _build_semantic_clusters(self, cancer_terms: List[str]):
        """Build semantic clusters of related cancer terms (optimized)."""
        logger.info("Building semantic clusters...")

        # Limit processing to avoid performance issues
        max_terms = min(len(cancer_terms), 1000)  # Process max 1000 terms for performance
        terms_to_process = cancer_terms[:max_terms]

        # Group terms by shared significant tokens
        token_groups = {}
        common_words = {'cancer', 'tumor', 'neoplasm', 'malignant', 'benign', 'of', 'the', 'and', 'or', 'with', 'cell', 'cells'}

        for term in terms_to_process:
            # Extract significant tokens (excluding very common words)
            tokens = [token.lower() for token in term.split()
                     if token.lower() not in common_words and len(token) > 2]

            # Limit tokens per term to avoid memory issues
            tokens = tokens[:5]  # Max 5 significant tokens per term

            # Create clusters based on shared tokens
            for token in tokens:
                if token not in token_groups:
                    token_groups[token] = []
                token_groups[token].append(term)

        # Build term clusters efficiently
        for term in cancer_terms:
            self.term_clusters[term] = set()

            if term in terms_to_process:
                tokens = [token.lower() for token in term.split()
                         if token.lower() not in common_words and len(token) > 2][:5]

                for token in tokens:
                    if token in token_groups:
                        # Add up to 10 related terms to avoid memory issues
                        related_terms = token_groups[token][:10]
                        self.term_clusters[term].update(related_terms)

                # Remove self from cluster
                self.term_clusters[term].discard(term)

        logger.info(f"Built semantic clusters for {len(self.term_clusters)} terms")

    def _build_mondo_hierarchical_context(self, cancer_terms: List[str]):
        """Build hierarchical context using MONDO ontology relationships (optimized)."""
        logger.info("Building MONDO hierarchical context (optimized)...")

        try:
            # Initialize MONDO adapter to get hierarchical relationships
            adapter = get_adapter("sqlite:obo:mondo")

            # Build a mapping of labels to IDs for efficient lookup
            logger.info("Building MONDO label-to-ID mapping...")
            label_to_id = {}

            # Get all cancer-related entities more efficiently
            cancer_root_id = self.cancer_mondo_id  # 'MONDO:0004992'
            cancer_descendants = list(adapter.descendants([cancer_root_id], predicates=[IS_A]))

            # Build label mapping only for cancer terms
            for entity_id in cancer_descendants:
                try:
                    label = adapter.label(entity_id)
                    if label:
                        label_to_id[label.lower()] = entity_id
                except:
                    continue

            logger.info(f"Built label mapping for {len(label_to_id)} MONDO cancer terms")

            # Now efficiently find ancestors for our cancer terms
            terms_with_ancestors = 0
            for term in cancer_terms:
                self.term_to_ancestors[term] = set()

                # Look up the term in our pre-built mapping
                term_lower = term.lower()
                if term_lower in label_to_id:
                    try:
                        entity_id = label_to_id[term_lower]
                        ancestors = list(adapter.ancestors([entity_id], predicates=[IS_A]))
                        ancestor_labels = []

                        for anc_id in ancestors:
                            anc_label = adapter.label(anc_id)
                            if anc_label:
                                ancestor_labels.append(anc_label)

                        self.term_to_ancestors[term].update(ancestor_labels)
                        if ancestor_labels:
                            terms_with_ancestors += 1

                    except Exception as e:
                        logger.debug(f"Could not get ancestors for '{term}': {e}")
                        continue

            logger.info(f"Built MONDO hierarchical context for {terms_with_ancestors} terms")

        except Exception as e:
            logger.warning(f"Could not build MONDO hierarchical context: {e}")
            # Fallback to empty ancestors
            for term in cancer_terms:
                self.term_to_ancestors[term] = set()

    def _calculate_string_similarity(self, term1: str, term2: str) -> Dict[str, float]:
        """Calculate various string similarity metrics between two terms using dynamic analysis."""
        term1_clean = term1.lower().strip()
        term2_clean = term2.lower().strip()

        # Levenshtein distance (normalized)
        levenshtein_ratio = SequenceMatcher(None, term1_clean, term2_clean).ratio()

        # Token overlap (Jaccard similarity)
        tokens1 = set(re.findall(r'\b\w+\b', term1_clean))
        tokens2 = set(re.findall(r'\b\w+\b', term2_clean))

        if len(tokens1) == 0 and len(tokens2) == 0:
            jaccard_similarity = 1.0
        elif len(tokens1) == 0 or len(tokens2) == 0:
            jaccard_similarity = 0.0
        else:
            intersection = len(tokens1.intersection(tokens2))
            union = len(tokens1.union(tokens2))
            jaccard_similarity = intersection / union if union > 0 else 0.0

        # Exact substring matching
        substring_bonus = 0.0
        if term1_clean in term2_clean or term2_clean in term1_clean:
            substring_bonus = 0.5

        # Dynamic keyword matching based on loaded cancer terms
        keyword_bonus = self._calculate_dynamic_keyword_bonus(term1_clean, term2_clean)

        return {
            "levenshtein_ratio": levenshtein_ratio,
            "jaccard_similarity": jaccard_similarity,
            "substring_bonus": substring_bonus,
            "keyword_bonus": keyword_bonus
        }

    def _calculate_dynamic_keyword_bonus(self, term1: str, term2: str) -> float:
        """Calculate keyword bonus based on shared significant terms from the cancer vocabulary."""
        if not hasattr(self, 'term_clusters') or not self.term_clusters:
            return 0.0

        # Extract significant tokens from both terms
        tokens1 = set(re.findall(r'\b\w+\b', term1))
        tokens2 = set(re.findall(r'\b\w+\b', term2))

        # Find shared tokens
        shared_tokens = tokens1.intersection(tokens2)

        # Calculate bonus based on significance of shared tokens
        bonus = 0.0
        for token in shared_tokens:
            # Check how many cancer terms contain this token
            token_frequency = sum(1 for term in self.term_clusters.keys() if token in term.lower())

            # More specific tokens (appearing in fewer terms) get higher bonus
            if token_frequency > 0:
                # Inverse frequency bonus - rare tokens are more significant
                token_significance = 1.0 / (1.0 + token_frequency / len(self.term_clusters))
                bonus += token_significance * 0.1

        return min(bonus, 0.3)  # Cap at 0.3

    def _get_biological_context_penalty(self, original_term: str, candidate_term: str) -> float:
        """Calculate biological context penalty using dynamic analysis of term relationships."""
        if not hasattr(self, 'term_clusters') or not self.term_clusters:
            return 0.0

        penalty = 0.0

        # 1. Semantic cluster mismatch penalty
        cluster_penalty = self._calculate_cluster_mismatch_penalty(original_term, candidate_term)

        # 2. Specificity mismatch penalty
        specificity_penalty = self._calculate_specificity_mismatch_penalty(original_term, candidate_term)

        # 3. Hierarchical distance penalty (if MONDO data available)
        hierarchical_penalty = self._calculate_hierarchical_distance_penalty(original_term, candidate_term)

        # Combine penalties (take maximum to avoid over-penalizing)
        penalty = max(cluster_penalty, specificity_penalty, hierarchical_penalty)

        logger.debug(f"Dynamic biological penalty for '{original_term}' -> '{candidate_term}': {penalty:.3f}")
        logger.debug(f"  Cluster: {cluster_penalty:.3f}, Specificity: {specificity_penalty:.3f}, Hierarchical: {hierarchical_penalty:.3f}")

        return penalty

    def _calculate_cluster_mismatch_penalty(self, original_term: str, candidate_term: str) -> float:
        """Calculate penalty based on semantic cluster mismatch."""
        if original_term not in self.term_clusters or candidate_term not in self.term_clusters:
            return 0.0

        original_cluster = self.term_clusters[original_term]
        candidate_cluster = self.term_clusters[candidate_term]

        # Check if terms share semantic clusters
        shared_cluster_terms = original_cluster.intersection(candidate_cluster)

        if len(shared_cluster_terms) == 0:
            # No shared semantic context - apply moderate penalty
            return 0.3

        # Calculate cluster overlap ratio
        total_cluster_terms = original_cluster.union(candidate_cluster)
        overlap_ratio = len(shared_cluster_terms) / len(total_cluster_terms) if total_cluster_terms else 0

        # Lower overlap = higher penalty
        return max(0.0, 0.3 * (1.0 - overlap_ratio))

    def _calculate_specificity_mismatch_penalty(self, original_term: str, candidate_term: str) -> float:
        """Calculate penalty for matching specific terms to generic ones."""
        if not hasattr(self, 'term_specificity'):
            return 0.0

        original_specificity = self.term_specificity.get(original_term, 0.5)
        candidate_specificity = self.term_specificity.get(candidate_term, 0.5)

        # Penalty when matching specific term to generic term
        specificity_diff = abs(original_specificity - candidate_specificity)

        # Higher penalty when a specific term is matched to a very generic term
        if original_specificity > 0.7 and candidate_specificity < 0.3:
            return 0.4
        elif specificity_diff > 0.5:
            return 0.2

        return 0.0

    def _calculate_hierarchical_distance_penalty(self, original_term: str, candidate_term: str) -> float:
        """Calculate penalty based on MONDO hierarchical distance."""
        if not hasattr(self, 'term_to_ancestors') or not self.term_to_ancestors:
            return 0.0

        original_ancestors = self.term_to_ancestors.get(original_term, set())
        candidate_ancestors = self.term_to_ancestors.get(candidate_term, set())

        if not original_ancestors or not candidate_ancestors:
            return 0.0

        # Check if terms share common ancestors
        shared_ancestors = original_ancestors.intersection(candidate_ancestors)

        if len(shared_ancestors) == 0:
            # No shared ancestors - high penalty for completely different lineages
            return 0.5

        # Calculate ancestor overlap ratio
        total_ancestors = original_ancestors.union(candidate_ancestors)
        overlap_ratio = len(shared_ancestors) / len(total_ancestors) if total_ancestors else 0

        # Lower overlap = higher penalty
        return max(0.0, 0.4 * (1.0 - overlap_ratio))

    def get_standard_cancer_type_subtype(self,
                                       cancer_type: str,
                                       cancer_type_context: Optional[str] = None,
                                       cancer_subtype: Optional[str] = None,
                                       cancer_subtype_context: Optional[str] = None) -> Union[List[str], Tuple[List[str], List[str]]]:
        """
        Get standardized cancer type and optionally subtype with comprehensive logging.

        Args:
            cancer_type: The cancer type to standardize
            cancer_type_context: Context for the cancer type
            cancer_subtype: Optional cancer subtype to standardize
            cancer_subtype_context: Context for the cancer subtype

        Returns:
            List of standardized cancer types, or tuple of (types, subtypes) if subtype provided
        """
        logger.info("=" * 80)
        logger.info("🏥 STARTING CANCER TYPE/SUBTYPE STANDARDIZATION")
        logger.info("=" * 80)

        if cancer_subtype is None:
            logger.info(f"📋 Standardizing cancer type only: '{cancer_type}'")
            result = self.standardize(cancer_type, cancer_type_context)
            logger.info(f"🎯 FINAL RESULT - Cancer Type: {result}")
            return result
        else:
            logger.info(f"📋 Standardizing both cancer type and subtype:")
            logger.info(f"   Type: '{cancer_type}'")
            logger.info(f"   Subtype: '{cancer_subtype}'")

            type_results = self.standardize(cancer_type, cancer_type_context)
            subtype_results = self.standardize(cancer_subtype, cancer_subtype_context)

            logger.info(f"🎯 FINAL RESULTS:")
            logger.info(f"   Cancer Type: {type_results}")
            logger.info(f"   Cancer Subtype: {subtype_results}")

            return (type_results, subtype_results)

    def process_test_data(self, test_data_dir: str = "markdown/results_sample") -> Dict[str, Any]:
        """
        Process test data from the results_sample directory to validate standardizer functionality.

        Args:
            test_data_dir: Directory containing test JSON files

        Returns:
            Dictionary containing processing results and statistics
        """
        logger.info("=" * 80)
        logger.info("🧪 PROCESSING TEST DATA FOR VALIDATION")
        logger.info("=" * 80)

        test_dir = Path(test_data_dir)
        if not test_dir.exists():
            logger.error(f"Test data directory not found: {test_data_dir}")
            return {"error": f"Test data directory not found: {test_data_dir}"}

        results = {
            "processed_files": [],
            "total_cancer_terms": 0,
            "standardization_results": [],
            "validation_failures": 0,
            "validation_successes": 0
        }

        # Process all JSON files in the test directory
        json_files = list(test_dir.glob("*.json"))
        logger.info(f"Found {len(json_files)} JSON files to process")

        for json_file in json_files:
            logger.info(f"📁 Processing file: {json_file.name}")

            try:
                with open(json_file, 'r') as f:
                    data = json.load(f)

                file_results = self._process_json_file_data(data, json_file.name)
                results["processed_files"].append(file_results)
                results["total_cancer_terms"] += file_results["cancer_terms_found"]
                results["standardization_results"].extend(file_results["standardizations"])
                results["validation_failures"] += file_results["validation_failures"]
                results["validation_successes"] += file_results["validation_successes"]

            except Exception as e:
                logger.error(f"Error processing file {json_file.name}: {e}")
                results["processed_files"].append({
                    "filename": json_file.name,
                    "error": str(e)
                })

        # Log summary statistics
        logger.info("=" * 80)
        logger.info("📊 TEST DATA PROCESSING SUMMARY")
        logger.info("=" * 80)
        logger.info(f"Files processed: {len(json_files)}")
        logger.info(f"Total cancer terms found: {results['total_cancer_terms']}")
        logger.info(f"Validation successes: {results['validation_successes']}")
        logger.info(f"Validation failures: {results['validation_failures']}")

        if results['total_cancer_terms'] > 0:
            success_rate = (results['validation_successes'] / results['total_cancer_terms']) * 100
            logger.info(f"Validation success rate: {success_rate:.1f}%")

        return results

    def _process_json_file_data(self, data: List[Dict], filename: str) -> Dict[str, Any]:
        """
        Process cancer terms from a single JSON file.

        Args:
            data: JSON data from file
            filename: Name of the file being processed

        Returns:
            Dictionary with processing results for this file
        """
        logger.info(f"🔍 Extracting cancer terms from {filename}")

        file_results = {
            "filename": filename,
            "cancer_terms_found": 0,
            "standardizations": [],
            "validation_failures": 0,
            "validation_successes": 0
        }

        for item in data:
            # Look for cancer type and subtype in model sections
            if isinstance(item, dict) and "model" in item:
                model_data = item["model"]

                # Extract cancer type
                if "cancer_type" in model_data and model_data["cancer_type"]:
                    cancer_type = model_data["cancer_type"]
                    logger.info(f"🎯 Found cancer_type: '{cancer_type}'")

                    # Standardize cancer type
                    standardized_types = self.standardize(cancer_type)
                    validation_passed = self._validate_standardization(cancer_type, standardized_types)

                    standardization_result = {
                        "original_term": cancer_type,
                        "field": "cancer_type",
                        "standardized_terms": standardized_types,
                        "validation_passed": validation_passed,
                        "source_file": filename
                    }

                    file_results["standardizations"].append(standardization_result)
                    file_results["cancer_terms_found"] += 1

                    if validation_passed:
                        file_results["validation_successes"] += 1
                    else:
                        file_results["validation_failures"] += 1

                # Extract cancer subtype
                if "cancer_subtype" in model_data and model_data["cancer_subtype"]:
                    cancer_subtype = model_data["cancer_subtype"]
                    logger.info(f"🎯 Found cancer_subtype: '{cancer_subtype}'")

                    # Standardize cancer subtype
                    standardized_subtypes = self.standardize(cancer_subtype)
                    validation_passed = self._validate_standardization(cancer_subtype, standardized_subtypes)

                    standardization_result = {
                        "original_term": cancer_subtype,
                        "field": "cancer_subtype",
                        "standardized_terms": standardized_subtypes,
                        "validation_passed": validation_passed,
                        "source_file": filename
                    }

                    file_results["standardizations"].append(standardization_result)
                    file_results["cancer_terms_found"] += 1

                    if validation_passed:
                        file_results["validation_successes"] += 1
                    else:
                        file_results["validation_failures"] += 1

        logger.info(f"📋 File {filename}: Found {file_results['cancer_terms_found']} cancer terms")
        return file_results

    def process_json_files_with_standardization(self, input_dir: str = "markdown/results_sample",
                                              output_dir: str = "standardized_results") -> Dict[str, Any]:
        """
        Process JSON files and output standardized versions that preserve exact structure
        while only standardizing cancer_type and cancer_subtype field values.

        Args:
            input_dir: Directory containing input JSON files
            output_dir: Directory to save standardized JSON files

        Returns:
            Dictionary containing processing results and statistics
        """
        logger.info("=" * 80)
        logger.info("🔄 PROCESSING JSON FILES FOR STANDARDIZATION")
        logger.info("=" * 80)

        # Create output directory if it doesn't exist
        os.makedirs(output_dir, exist_ok=True)
        logger.info(f"📁 Output directory: {output_dir}")

        results = {
            "processed_files": [],
            "total_files_processed": 0,
            "total_fields_standardized": 0,
            "files_with_cancer_fields": 0,
            "files_without_cancer_fields": 0
        }

        # Find all JSON files in the input directory
        input_files = []
        if os.path.exists(input_dir):
            for file in os.listdir(input_dir):
                if file.endswith('.json'):
                    input_files.append(os.path.join(input_dir, file))

        if not input_files:
            logger.warning(f"No JSON files found in {input_dir}")
            return results

        logger.info(f"Found {len(input_files)} JSON files to process")

        for input_file_path in input_files:
            filename = os.path.basename(input_file_path)
            output_file_path = os.path.join(output_dir, filename)

            logger.info(f"📄 Processing file: {filename}")

            try:
                # Load the JSON file
                with open(input_file_path, 'r', encoding='utf-8') as f:
                    original_data = json.load(f)

                # Create a deep copy to preserve original structure
                standardized_data = json.loads(json.dumps(original_data))

                # Track standardization results for this file
                file_results = {
                    "filename": filename,
                    "input_path": input_file_path,
                    "output_path": output_file_path,
                    "fields_standardized": [],
                    "cancer_fields_found": 0,
                    "standardization_successful": False
                }

                # Recursively find and standardize cancer fields
                fields_standardized = self._standardize_cancer_fields_recursive(
                    standardized_data, original_data, [], file_results
                )

                file_results["cancer_fields_found"] = len(fields_standardized)
                file_results["standardization_successful"] = len(fields_standardized) > 0

                if len(fields_standardized) > 0:
                    # Save the standardized JSON file
                    with open(output_file_path, 'w', encoding='utf-8') as f:
                        json.dump(standardized_data, f, indent=2, ensure_ascii=False)

                    logger.info(f"✅ Standardized {len(fields_standardized)} cancer fields in {filename}")
                    logger.info(f"💾 Saved standardized file: {output_file_path}")
                    results["files_with_cancer_fields"] += 1
                    results["total_fields_standardized"] += len(fields_standardized)
                else:
                    logger.warning(f"⚠️ No cancer_type or cancer_subtype fields found in {filename}")
                    logger.info(f"📋 File structure preserved without changes")
                    # Still save the file to maintain consistency
                    with open(output_file_path, 'w', encoding='utf-8') as f:
                        json.dump(standardized_data, f, indent=2, ensure_ascii=False)
                    results["files_without_cancer_fields"] += 1

                results["processed_files"].append(file_results)
                results["total_files_processed"] += 1

            except Exception as e:
                logger.error(f"❌ Error processing {filename}: {str(e)}")
                continue

        # Log summary
        logger.info("=" * 80)
        logger.info("📊 STANDARDIZATION SUMMARY")
        logger.info("=" * 80)
        logger.info(f"📁 Total files processed: {results['total_files_processed']}")
        logger.info(f"🎯 Files with cancer fields: {results['files_with_cancer_fields']}")
        logger.info(f"⚠️ Files without cancer fields: {results['files_without_cancer_fields']}")
        logger.info(f"🔄 Total fields standardized: {results['total_fields_standardized']}")
        logger.info(f"📂 Output directory: {output_dir}")

        # Save processing results
        results_filename = os.path.join(output_dir, f"standardization_results_{self.embedding_model.replace('-', '_')}.json")
        with open(results_filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        logger.info(f"💾 Processing results saved to: {results_filename}")

        return results

    def process_single_json_file(self, input_file: str, output_file: str) -> Dict[str, Any]:
        """
        Process a single JSON file and output standardized version that preserves exact structure
        while only standardizing cancer_type and cancer_subtype field values.

        Args:
            input_file: Path to input JSON file
            output_file: Path to output standardized JSON file

        Returns:
            Dictionary containing processing results
        """
        logger.info("=" * 80)
        logger.info(f"🔄 PROCESSING SINGLE JSON FILE: {input_file}")
        logger.info("=" * 80)

        if not os.path.exists(input_file):
            error_msg = f"Input file not found: {input_file}"
            logger.error(f"❌ {error_msg}")
            return {"error": error_msg}

        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(output_file)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        try:
            # Load the JSON file
            with open(input_file, 'r', encoding='utf-8') as f:
                original_data = json.load(f)

            # Create a deep copy to preserve original structure
            standardized_data = json.loads(json.dumps(original_data))

            # Track standardization results
            file_results = {
                "filename": os.path.basename(input_file),
                "input_path": input_file,
                "output_path": output_file,
                "fields_standardized": [],
                "cancer_fields_found": 0,
                "standardization_successful": False
            }

            # Recursively find and standardize cancer fields
            fields_standardized = self._standardize_cancer_fields_recursive(
                standardized_data, original_data, [], file_results
            )

            file_results["cancer_fields_found"] = len(fields_standardized)
            file_results["standardization_successful"] = len(fields_standardized) > 0

            # Save the standardized JSON file
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(standardized_data, f, indent=2, ensure_ascii=False)

            if len(fields_standardized) > 0:
                logger.info(f"✅ Standardized {len(fields_standardized)} cancer fields")
                logger.info(f"💾 Saved standardized file: {output_file}")
            else:
                logger.warning(f"⚠️ No cancer_type or cancer_subtype fields found")
                logger.info(f"📋 File structure preserved without changes")
                logger.info(f"💾 Saved unchanged file: {output_file}")

            return file_results

        except Exception as e:
            error_msg = f"Error processing file {input_file}: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {"error": error_msg}

    def _standardize_cancer_fields_recursive(self, data: Any, original_data: Any,
                                           path: List[str], file_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Recursively search through JSON structure to find and standardize cancer_type and cancer_subtype fields.

        Args:
            data: Current data structure being processed (will be modified)
            original_data: Original data structure (for reference)
            path: Current path in the JSON structure
            file_results: File processing results to update

        Returns:
            List of standardization results
        """
        standardizations = []

        if isinstance(data, dict):
            for key, value in data.items():
                current_path = path + [key]

                # Check if this is a cancer field we need to standardize
                if key in ["cancer_type", "cancer_subtype"] and value and isinstance(value, str):
                    path_str = " -> ".join(current_path)
                    logger.info(f"🎯 Found {key} at path '{path_str}': '{value}'")

                    try:
                        # Standardize the cancer term
                        standardized_terms = self.standardize(value)

                        if standardized_terms:
                            # Use the top standardized term as the replacement
                            new_value = standardized_terms[0]

                            # Update the data structure
                            data[key] = new_value

                            # Log the standardization
                            logger.info(f"🔄 STANDARDIZED '{value}' -> '{new_value}' at path '{path_str}'")

                            # Record the standardization
                            standardization_info = {
                                "field": key,
                                "path": path_str,
                                "original_value": value,
                                "standardized_value": new_value,
                                "all_standardized_terms": standardized_terms
                            }

                            standardizations.append(standardization_info)
                            file_results["fields_standardized"].append(standardization_info)
                        else:
                            logger.warning(f"⚠️ No standardization found for '{value}' at path '{path_str}'")

                    except Exception as e:
                        logger.error(f"❌ Error standardizing '{value}' at path '{path_str}': {str(e)}")

                # Recursively process nested structures
                elif isinstance(value, (dict, list)):
                    nested_standardizations = self._standardize_cancer_fields_recursive(
                        value, original_data, current_path, file_results
                    )
                    standardizations.extend(nested_standardizations)

        elif isinstance(data, list):
            for i, item in enumerate(data):
                current_path = path + [str(i)]
                if isinstance(item, (dict, list)):
                    nested_standardizations = self._standardize_cancer_fields_recursive(
                        item, original_data, current_path, file_results
                    )
                    standardizations.extend(nested_standardizations)

        return standardizations


def main():
    """Command line interface for cancer term standardization."""
    parser = argparse.ArgumentParser(description="Standardize cancer terms using OpenAI embeddings")
    parser.add_argument("--term", help="Cancer term to standardize")
    parser.add_argument("--context", help="Context for the cancer term")
    parser.add_argument("--detailed", action="store_true",
                       help="Use detailed two-stage standardization with LLM biological filtering and explanations")
    parser.add_argument("--cache-dir", default="cache", help="Directory for cached files")
    parser.add_argument("--num-output", type=int, default=10, help="Number of results to return")
    parser.add_argument("--embedding-model", default="text-embedding-3-large",
                       choices=["text-embedding-3-large", "text-embedding-ada-002"],
                       help="OpenAI embedding model to use")
    parser.add_argument("--test-data", action="store_true",
                       help="Process test data from markdown/results_sample directory")
    parser.add_argument("--test-data-dir", default="markdown/results_sample",
                       help="Directory containing test data JSON files")
    parser.add_argument("--standardize-json", action="store_true",
                       help="Process JSON files and output standardized versions")
    parser.add_argument("--input-dir", default="markdown/results_sample",
                       help="Input directory containing JSON files to standardize")
    parser.add_argument("--output-dir", default="standardized_results",
                       help="Output directory for standardized JSON files")
    parser.add_argument("--input-file", help="Single input JSON file to standardize")
    parser.add_argument("--output-file", help="Output path for single standardized JSON file")

    args = parser.parse_args()

    # Initialize standardizer
    standardizer = CancerTermStandardizerOpenAI(
        cache_dir=args.cache_dir,
        num_output=args.num_output,
        embedding_model=args.embedding_model
    )

    if args.standardize_json:
        # Process JSON files for standardization
        if args.input_file and args.output_file:
            # Single file mode
            logger.info(f"Processing single file: {args.input_file} -> {args.output_file}")
            results = standardizer.process_single_json_file(args.input_file, args.output_file)

            if "error" in results:
                logger.error(f"Failed to process file: {results['error']}")
            else:
                logger.info("✅ Single file processing completed successfully")
        else:
            # Batch mode
            logger.info(f"Processing JSON files from {args.input_dir} to {args.output_dir}")
            results = standardizer.process_json_files_with_standardization(args.input_dir, args.output_dir)
            logger.info("✅ Batch processing completed successfully")

    elif args.test_data:
        # Process test data
        logger.info("Processing test data to validate standardizer functionality...")
        results = standardizer.process_test_data(args.test_data_dir)

        # Save results to file
        output_file = f"standardization_test_results_{args.embedding_model.replace('-', '_')}.json"
        with open(output_file, 'w') as f:
            json.dump(results, f, indent=2)
        logger.info(f"Test results saved to: {output_file}")

    elif args.term:
        # Standardize single term
        if hasattr(args, 'detailed') and args.detailed:
            # Use detailed standardization with explanations
            results = standardizer.standardize_with_explanations(args.term, args.context)

            print(f"Standardized terms for '{args.term}':")
            for i, result in enumerate(results, 1):
                print(f"{i}. {result['term']} (confidence: {result['confidence']:.2f}) - {result['explanation']}")
        else:
            # Use standard standardization
            results = standardizer.standardize_with_explanations(args.term, args.context)

            print(f"Standardized terms for '{args.term}':")
            for i, result in enumerate(results, 1):
                print(f"{i}. {result['term']} (confidence: {result['confidence']:.2f}) - {result['explanation']}")
    else:
        parser.print_help()
        print("\nExample usage:")
        print("  # Standardize a single term (basic):")
        print("  python cancer_term_standardizer_openai.py --term 'breast cancer'")
        print("  # Standardize with detailed LLM biological filtering:")
        print("  python cancer_term_standardizer_openai.py --term 'hematological malignancy' --detailed")
        print("  # Run validation tests:")
        print("  python cancer_term_standardizer_openai.py --test-data")
        print("  # Standardize JSON files (batch mode):")
        print("  python cancer_term_standardizer_openai.py --standardize-json")
        print("  # Standardize JSON files with custom directories:")
        print("  python cancer_term_standardizer_openai.py --standardize-json --input-dir input/ --output-dir output/")
        print("  # Standardize single JSON file:")
        print("  python cancer_term_standardizer_openai.py --standardize-json --input-file input.json --output-file output.json")


if __name__ == "__main__":
    main()
